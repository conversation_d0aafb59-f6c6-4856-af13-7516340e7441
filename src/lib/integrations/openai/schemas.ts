/**
 * OpenAI Responses API Structured Output Schemas
 * Zod schemas for German financial content processing with guaranteed JSON schema adherence
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-06-29
 * @sprint Sprint 2: OpenAI Responses API Upgrade
 */

import { z } from 'zod';

/**
 * Canadian Market Relevance Analysis Schema
 * Structured output for determining Canadian market relevance of German financial content
 */
export const RelevanceAnalysisSchema = z.object({
  relevanceScore: z
    .number()
    .min(0)
    .max(100)
    .describe('Canadian market relevance score (0-100)'),
  reasoning: z
    .string()
    .min(10)
    .max(500)
    .describe('Brief explanation of relevance assessment'),
  keyFactors: z
    .array(z.string())
    .min(1)
    .max(10)
    .describe('Key factors affecting Canadian market relevance'),
  category: z
    .enum(['high', 'medium', 'low'])
    .describe('Relevance category based on score'),
  canadianCompanies: z
    .array(z.string())
    .nullable()
    .optional()
    .describe('Canadian companies mentioned in the article'),
  marketSectors: z
    .array(z.string())
    .nullable()
    .optional()
    .describe('Market sectors that could be affected'),
  keywords: z
    .array(z.string())
    .min(3)
    .max(15)
    .describe('Relevant keywords for Canadian market context'),
  confidence: z
    .number()
    .min(0)
    .max(100)
    .describe('Confidence level in the analysis'),
});

/**
 * Content Translation Schema
 * Structured output for German to English translation with quality metrics
 */
export const ContentTranslationSchema = z.object({
  translatedTitle: z
    .string()
    .min(10)
    .max(200)
    .describe('Clean English title without emojis or special characters'),
  translatedContent: z
    .string()
    .min(100)
    .describe('English translation in markdown format preserving structure'),
  translationQuality: z
    .enum(['excellent', 'good', 'fair'])
    .describe('Translation quality assessment'),
  preservedStructure: z
    .boolean()
    .describe('Whether original German structure was preserved'),
  wordCount: z.number().min(50).describe('Word count of translated content'),
  germanContext: z
    .string()
    .nullable()
    .optional()
    .describe('Important German market context preserved'),
});

/**
 * German Content Enhancement Schema
 * Structured output for enhancing German content for Canadian market context
 */
export const ContentEnhancementSchema = z.object({
  enhancedTitle: z
    .string()
    .min(10)
    .max(200)
    .describe('Enhanced German title optimized for Canadian market'),
  enhancedContent: z
    .string()
    .min(100)
    .describe('Enhanced German content in markdown format'),
  summary: z.string().min(50).max(300).describe('3-sentence summary in German'),
  keyInsights: z
    .array(z.string())
    .min(3)
    .max(10)
    .describe('Key insights for Canadian investors in German'),
  marketImplications: z
    .string()
    .min(50)
    .max(500)
    .describe('Implications for Canadian market in German'),
  enhancementQuality: z
    .enum(['excellent', 'good', 'fair'])
    .describe('Enhancement quality assessment'),
  canadianContext: z
    .string()
    .min(20)
    .max(300)
    .describe('Canadian market context added to content'),
  addedValue: z
    .array(z.string())
    .min(1)
    .max(5)
    .describe('Specific value additions made for Canadian audience'),
});

/**
 * Dual-Language Enhancement Schema
 * Single API call that enhances German content and provides English translation
 * Ensures consistency between German and English versions for multilingual website
 */
export const DualLanguageEnhancementSchema = z.object({
  enhancedGerman: z.object({
    title: z
      .string()
      .min(10)
      .max(200)
      .describe('Enhanced German title with Canadian market context'),
    content: z
      .string()
      .min(100)
      .describe(
        'Enhanced German content in clean HTML format suitable for Lexical conversion'
      ),
    summary: z
      .string()
      .min(50)
      .max(450)
      .describe('German summary (max 450 characters)'),
    keyInsights: z
      .array(z.string())
      .min(3)
      .max(10)
      .describe('Key insights for Canadian investors in German'),
    canadianContext: z
      .string()
      .min(20)
      .max(300)
      .describe('Canadian market context added to content'),
  }),
  enhancedEnglish: z.object({
    title: z
      .string()
      .min(10)
      .max(200)
      .describe('English translation of enhanced German title'),
    content: z
      .string()
      .min(100)
      .describe(
        'English translation of enhanced German content in clean HTML format'
      ),
    summary: z
      .string()
      .min(50)
      .max(450)
      .describe('English summary (max 450 characters)'),
    keyInsights: z
      .array(z.string())
      .min(3)
      .max(10)
      .describe('Key insights for Canadian investors in English'),
    canadianContext: z
      .string()
      .min(20)
      .max(300)
      .describe('Canadian market context in English'),
  }),
  consistency: z.object({
    structureMatches: z
      .boolean()
      .describe('Whether German and English content have identical structure'),
    contentAlignment: z
      .enum(['perfect', 'good', 'fair'])
      .describe('How well the content aligns between languages'),
    translationQuality: z
      .enum(['excellent', 'good', 'fair'])
      .describe('Quality of the translation'),
  }),
  enhancement: z.object({
    canadianRelevance: z
      .number()
      .min(0)
      .max(100)
      .describe('Canadian market relevance score'),
    addedValue: z
      .array(z.string())
      .min(1)
      .max(5)
      .describe('Specific value additions made for Canadian audience'),
    marketImplications: z
      .string()
      .min(50)
      .max(500)
      .describe('Implications for Canadian market'),
  }),
});

/**
 * Company Extraction Schema
 * Structured output for extracting companies and their ticker symbols from financial content
 */
export const CompanyExtractionSchema = z.object({
  extractedCompanies: z
    .array(
      z.object({
        name: z
          .string()
          .min(2)
          .max(100)
          .describe('Company name as mentioned in the article'),
        tickerSymbol: z
          .string()
          .nullable()
          .optional()
          .describe('Stock ticker symbol if identifiable (e.g., AAPL, TSLA)'),
        exchange: z
          .string()
          .nullable()
          .optional()
          .describe('Stock exchange if identifiable (e.g., NYSE, NASDAQ, DAX)'),
        sector: z
          .string()
          .nullable()
          .optional()
          .describe('Industry sector if identifiable'),
        confidence: z
          .number()
          .min(0)
          .max(100)
          .describe('Confidence level in ticker symbol matching (0-100)'),
      })
    )
    .min(0)
    .max(20)
    .describe('Companies mentioned in the article with ticker symbols'),
  extractionMetadata: z.object({
    totalCompaniesFound: z
      .number()
      .min(0)
      .describe('Total number of companies identified'),
    tickerSymbolsMatched: z
      .number()
      .min(0)
      .describe('Number of companies with successfully matched ticker symbols'),
    extractionQuality: z
      .enum(['excellent', 'good', 'fair', 'poor'])
      .describe('Quality assessment of company extraction'),
    germanContext: z
      .boolean()
      .describe('Whether German company context was preserved'),
  }),
});

/**
 * English-Only Content Enhancement Schema
 * Simplified schema for direct German→English enhanced content transformation
 * Replaces dual-language system with focused English enhancement output
 * Now includes company extraction and ticker symbol matching
 */
export const EnglishOnlyEnhancementSchema = z.object({
  // Enhanced English Content (Direct transformation from German)
  enhancedContent: z.object({
    title: z
      .string()
      .min(10)
      .max(60)
      .describe('Enhanced English title (50-60 characters, SEO optimized)'),
    content: z
      .string()
      .min(200) // 🔧 TEMPORARY: Relaxed from 600 to 200 for testing
      .max(2000)
      .describe(
        'Enhanced English content in clean HTML format (600-750 words)'
      ),
    summary: z
      .string()
      .min(200)
      .max(400)
      .describe('English summary (200-400 characters, short paragraph)'),
    keyInsights: z
      .array(z.string())
      .min(3)
      .max(4)
      .describe('Key insights for international readers'),
    keywords: z
      .array(z.string())
      .min(5)
      .max(10)
      .describe('SEO-optimized English keywords'),
    relatedCompanies: z
      .array(
        z.object({
          name: z
            .string()
            .min(2)
            .max(100)
            .describe('Company name as mentioned in content'),
          tickerSymbol: z
            .string()
            .nullable()
            .optional()
            .describe('Stock ticker symbol if identifiable'),
          exchange: z
            .string()
            .nullable()
            .optional()
            .describe('Stock exchange if identifiable'),
          relevance: z
            .enum(['high', 'medium', 'low'])
            .describe('Relevance to the article content'),
          confidence: z
            .number()
            .min(0)
            .max(100)
            .describe('Confidence in ticker symbol accuracy'),
        })
      )
      .min(0)
      .max(15)
      .describe('Companies mentioned in the article with ticker symbols'),
  }),

  // Quality Assessment
  quality: z.object({
    contentScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Content quality score (0-100)'),
    relevanceScore: z
      .number()
      .min(0)
      .max(100)
      .describe('International market relevance score (0-100)'),
    enhancementQuality: z
      .enum(['excellent', 'good', 'fair'])
      .describe('Overall enhancement quality assessment'),
    readabilityScore: z
      .number()
      .min(0)
      .max(100)
      .describe('English readability score for international audience'),
    companyExtractionQuality: z
      .enum(['excellent', 'good', 'fair', 'poor'])
      .describe('Quality of company extraction and ticker matching'),
  }),

  // Processing Metadata
  processing: z.object({
    originalWordCount: z
      .number()
      .min(0)
      .describe('Word count of original German content'),
    enhancedWordCount: z
      .number()
      .min(200) // 🔧 TEMPORARY: Relaxed from 600 to 200 for testing
      .max(750)
      .describe('Word count of enhanced English content'),
    transformationType: z
      .enum(['enhancement', 'expansion', 'restructuring'])
      .describe('Type of transformation applied'),
    internationalContext: z
      .string()
      .min(20)
      .max(200)
      .describe('Context added for international readers'),
    companiesExtracted: z
      .number()
      .min(0)
      .describe('Number of companies extracted from content'),
    tickerSymbolsMatched: z
      .number()
      .min(0)
      .describe('Number of companies with successfully matched ticker symbols'),
  }),
});

/**
 * Streamlined Unified Content Enhancement Schema
 * Optimized for performance - focuses on essential outputs only
 * Consolidates 7 original prompts with minimal overhead
 */
export const UnifiedContentEnhancementSchema = z.object({
  // Enhanced German Content
  enhancedGerman: z.object({
    title: z.string().min(10).max(200).describe('Enhanced German title'),
    content: z
      .string()
      .min(100)
      .describe('Enhanced German content in clean HTML format'),
    summary: z
      .string()
      .min(50)
      .max(150)
      .describe('German summary (max 150 characters)'),
    keywords: z
      .array(z.string())
      .min(5)
      .max(12)
      .describe('German SEO keywords'),
  }),

  // Enhanced English Content
  enhancedEnglish: z.object({
    title: z
      .string()
      .min(10)
      .max(200)
      .describe('English translation of enhanced title'),
    content: z
      .string()
      .min(100)
      .describe('English translation in clean HTML format'),
    summary: z
      .string()
      .min(50)
      .max(150)
      .describe('English summary (max 150 characters)'),
    keywords: z
      .array(z.string())
      .min(5)
      .max(12)
      .describe('English SEO keywords'),
  }),

  // Quality Scores
  quality: z.object({
    contentScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Content quality score (0-100)'),
    relevanceScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Market relevance score (0-100)'),
    translationQuality: z
      .enum(['excellent', 'good', 'fair'])
      .describe('Translation quality assessment'),
  }),
});

/**
 * Title Optimization Schema
 * Structured output for cleaning and optimizing titles for SEO
 */
export const TitleOptimizationSchema = z.object({
  cleanedTitle: z
    .string()
    .min(5)
    .max(200)
    .describe('Title with foreign characters and emojis removed'),
  seoOptimized: z
    .string()
    .min(5)
    .max(200)
    .describe('SEO-optimized version of the title'),
  charactersCleaned: z
    .array(z.string())
    .describe('List of characters that were cleaned'),
  optimization: z.object({
    removedEmojis: z.boolean().describe('Whether emojis were removed'),
    removedSpecialChars: z
      .boolean()
      .describe('Whether special characters were removed'),
    improvedReadability: z
      .boolean()
      .describe('Whether readability was improved'),
    seoScore: z.number().min(0).max(100).describe('SEO optimization score'),
    lengthOptimal: z
      .boolean()
      .describe('Whether title length is optimal (30-60 chars)'),
  }),
  suggestions: z
    .array(z.string())
    .max(5)
    .describe('Additional optimization suggestions'),
  originalLength: z.number().describe('Original title length'),
  optimizedLength: z.number().describe('Optimized title length'),
});

/**
 * Financial Content Processing Schema
 * Comprehensive schema for processing German financial content with all metadata
 */
export const FinancialContentSchema = z.object({
  processedContent: z.object({
    title: z.string().min(5).max(200).describe('Processed and optimized title'),
    content: z
      .string()
      .min(100)
      .describe('Processed content in markdown format'),
    summary: z.string().min(50).max(300).describe('Article summary'),
    keyPoints: z
      .array(z.string())
      .min(3)
      .max(10)
      .describe('Key points from the article'),
  }),
  analysis: z.object({
    relevanceScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Canadian market relevance score'),
    category: z
      .enum(['markets', 'companies', 'economy', 'technology', 'other'])
      .describe('Content category'),
    sentiment: z
      .enum(['positive', 'negative', 'neutral'])
      .describe('Overall sentiment'),
    complexity: z
      .enum(['basic', 'intermediate', 'advanced'])
      .describe('Content complexity level'),
  }),
  financialData: z
    .object({
      stockPrices: z
        .array(
          z.object({
            symbol: z.string(),
            price: z.string(),
            change: z.string().nullable().optional(),
          })
        )
        .nullable()
        .optional()
        .describe('Stock prices mentioned'),
      marketIndices: z
        .array(z.string())
        .nullable()
        .optional()
        .describe('Market indices referenced'),
      currencies: z
        .array(z.string())
        .nullable()
        .optional()
        .describe('Currencies mentioned'),
      financialMetrics: z
        .array(z.string())
        .nullable()
        .optional()
        .describe('Financial metrics discussed'),
    })
    .nullable()
    .optional(),
  metadata: z.object({
    author: z.string().nullable().optional().describe('Article author'),
    publishDate: z.string().nullable().optional().describe('Publication date'),
    language: z.string().default('de').describe('Content language'),
    wordCount: z.number().min(0).describe('Word count'),
    readingTime: z
      .number()
      .min(1)
      .describe('Estimated reading time in minutes'),
    tags: z.array(z.string()).max(10).describe('Content tags'),
  }),
});

/**
 * Batch Processing Schema
 * For processing multiple articles in a single API call
 */
export const BatchProcessingSchema = z.object({
  results: z.array(
    z.object({
      id: z.string().describe('Unique identifier for the article'),
      success: z.boolean().describe('Whether processing was successful'),
      data: FinancialContentSchema.nullable()
        .optional()
        .describe('Processed data if successful'),
      error: z
        .string()
        .nullable()
        .optional()
        .describe('Error message if processing failed'),
    })
  ),
  summary: z.object({
    totalProcessed: z
      .number()
      .min(0)
      .describe('Total number of articles processed'),
    successful: z
      .number()
      .min(0)
      .describe('Number of successfully processed articles'),
    failed: z.number().min(0).describe('Number of failed articles'),
    averageRelevanceScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Average relevance score'),
  }),
});

// Export all schemas for use in API calls
export const schemas = {
  relevanceAnalysis: RelevanceAnalysisSchema,
  contentTranslation: ContentTranslationSchema,
  contentEnhancement: ContentEnhancementSchema,
  dualLanguageEnhancement: DualLanguageEnhancementSchema,
  unifiedContentEnhancement: UnifiedContentEnhancementSchema,
  englishOnlyEnhancement: EnglishOnlyEnhancementSchema,
  companyExtraction: CompanyExtractionSchema,
  titleOptimization: TitleOptimizationSchema,
  financialContent: FinancialContentSchema,
  batchProcessing: BatchProcessingSchema,
} as const;

// Export schema types for TypeScript
export type RelevanceAnalysis = z.infer<typeof RelevanceAnalysisSchema>;
export type ContentTranslation = z.infer<typeof ContentTranslationSchema>;
export type ContentEnhancement = z.infer<typeof ContentEnhancementSchema>;
export type DualLanguageEnhancement = z.infer<
  typeof DualLanguageEnhancementSchema
>;
export type UnifiedContentEnhancement = z.infer<
  typeof UnifiedContentEnhancementSchema
>;
export type EnglishOnlyEnhancement = z.infer<
  typeof EnglishOnlyEnhancementSchema
>;
export type CompanyExtraction = z.infer<typeof CompanyExtractionSchema>;
export type TitleOptimization = z.infer<typeof TitleOptimizationSchema>;
export type FinancialContent = z.infer<typeof FinancialContentSchema>;
export type BatchProcessing = z.infer<typeof BatchProcessingSchema>;

/**
 * Transformation function for backward compatibility
 * Converts streamlined unified result to legacy DualLanguageEnhancement format
 */
export function transformToLegacyFormat(
  unifiedResult: UnifiedContentEnhancement
): DualLanguageEnhancement {
  return {
    enhancedGerman: {
      title: unifiedResult.enhancedGerman.title,
      content: unifiedResult.enhancedGerman.content,
      summary: unifiedResult.enhancedGerman.summary,
      keyInsights: [], // Simplified - no longer generated
      canadianContext: 'Enhanced for global markets', // Default context
    },
    enhancedEnglish: {
      title: unifiedResult.enhancedEnglish.title,
      content: unifiedResult.enhancedEnglish.content,
      summary: unifiedResult.enhancedEnglish.summary,
      keyInsights: [], // Simplified - no longer generated
      canadianContext: 'Enhanced for global markets', // Default context
    },
    consistency: {
      structureMatches: true, // Assumed true for streamlined system
      contentAlignment: 'good' as const,
      translationQuality: unifiedResult.quality.translationQuality,
    },
    enhancement: {
      canadianRelevance: unifiedResult.quality.relevanceScore,
      addedValue: ['Content enhanced and optimized'], // Simplified
      marketImplications: 'Content optimized for global financial markets', // Default
    },
  };
}

/**
 * PayloadCMS field mapping for unified schema
 */
export const payloadFieldMapping = {
  'enhancedTab.enhancedGermanContent': 'enhancedGerman.content',
  'enhancedTab.enhancedEnglishContent': 'enhancedEnglish.content',
  'enhancedTab.enhancedGermanSummary': 'enhancedGerman.summary',
  'enhancedTab.enhancedEnglishSummary': 'enhancedEnglish.summary',
  'enhancedTab.germanInsights': 'enhancedGerman.insights',
  'enhancedTab.englishInsights': 'enhancedEnglish.insights',
  'seoTab.germanSeoTitle': 'enhancedGerman.metadata.seoTitle',
  'seoTab.englishSeoTitle': 'enhancedEnglish.metadata.seoTitle',
  'seoTab.germanDescription': 'enhancedGerman.metadata.description',
  'seoTab.englishDescription': 'enhancedEnglish.metadata.description',
  'seoTab.germanKeywords': 'enhancedGerman.metadata.keywords',
  'seoTab.englishKeywords': 'enhancedEnglish.metadata.keywords',
} as const;
