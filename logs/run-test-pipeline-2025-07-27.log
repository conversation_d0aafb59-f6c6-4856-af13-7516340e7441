{"timestamp":"2025-07-27T21:46:03.687Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T21:46:03.687Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T21:46:03.686Z","pipeline":"run-test-pipeline","testUrlCount":9,"testUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"},{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"},{"url":"https://www.manager-magazin.de/politik/deutschland/deutschland-braucht-neue-schulden-regeln-und-mehr...","domain":"www.manager-magazin.de"},{"url":"https://www.spiegel.de/wirtschaft/unternehmen/deutsche-bank-meldet-rekordgewinne-im-dritten-quartal-...","domain":"www.spiegel.de"},{"url":"https://www.bbc.co.uk/news/articles/c79q8g7q283o","domain":"www.bbc.co.uk"}]}}
{"timestamp":"2025-07-27T21:46:03.699Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T21:46:03.699Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T21:46:03.706Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"Test Article","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:46:03.705Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T21:46:03.705Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-neta...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T21:46:03.706Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":9,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:46:03.705Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":9}}
{"timestamp":"2025-07-27T21:46:03.705Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":9,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:46:03.706Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:46:03.719Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:46:03.719Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:46:03.726Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:46:03.726Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:46:24.452Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","contentLength":31949,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:46:24.452Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":20733,"success":true,"contentLength":31949,"extractionMethod":"stealth","wordCount":320},"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah"}}
{"timestamp":"2025-07-27T21:47:01.025Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":1,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","reason":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:47:01.025Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","duration":57277,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue"}}
{"timestamp":"2025-07-27T21:47:00.984Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","processingDuration":57277,"urlIndex":1,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:48:20.730Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":137004,"success":true,"contentLength":48,"extractionMethod":"structured","wordCount":58},"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d"}}
{"timestamp":"2025-07-27T21:48:20.730Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","contentLength":48,"hasPublishedDate":false,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:48:26.733Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T21:48:26.733Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T21:48:26.733Z","pipeline":"run-test-pipeline","testUrlCount":9,"testUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"},{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"},{"url":"https://www.manager-magazin.de/politik/deutschland/deutschland-braucht-neue-schulden-regeln-und-mehr...","domain":"www.manager-magazin.de"},{"url":"https://www.spiegel.de/wirtschaft/unternehmen/deutsche-bank-meldet-rekordgewinne-im-dritten-quartal-...","domain":"www.spiegel.de"},{"url":"https://www.bbc.co.uk/news/articles/c79q8g7q283o","domain":"www.bbc.co.uk"}]}}
{"timestamp":"2025-07-27T21:48:26.737Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T21:48:26.737Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T21:48:26.742Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T21:48:26.742Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":9}}
{"timestamp":"2025-07-27T21:48:26.743Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"Test Article","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:48:26.742Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-neta...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T21:48:26.743Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":9,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:48:26.743Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":9,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:48:26.743Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:48:26.759Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:48:26.759Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:48:26.766Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:48:26.766Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:49:01.756Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","duration":178007,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue"}}
{"timestamp":"2025-07-27T21:49:01.800Z","level":"INFO","component":"run-test-pipeline","message":"⏳ Rate limiting delay","context":{"pipeline":"run-test-pipeline","delayMs":3000,"reason":"OpenAI rate limiting"}}
{"timestamp":"2025-07-27T21:49:01.713Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","processingDuration":178007,"urlIndex":2,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:49:01.757Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","reason":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:49:04.802Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":2,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-sc...","domain":"www.onvista.de"}]}}
{"timestamp":"2025-07-27T21:49:04.803Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-3","pipeline":"run-test-pipeline","urlIndex":3,"extractedTitle":"zurueckhaltung tesla aktie unter druck schwache verkaufszahlen und aerger mit trump belasten","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T21:49:04.803Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":4,"totalUrls":9,"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T21:49:04.803Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":3,"totalUrls":9,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T21:49:04.803Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-starten-41121301-19-26405537","processingId":"test-url-4","pipeline":"run-test-pipeline","urlIndex":4,"extractedTitle":"dax vorboerse dax duerfte mit leichtem schwung in den juli starten","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T21:49:04.825Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:49:04.825Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:49:04.827Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:49:04.827Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:49:38.346Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":33519,"success":true,"contentLength":73520,"extractionMethod":"structured","wordCount":1422},"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza"}}
{"timestamp":"2025-07-27T21:49:38.346Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","contentLength":73520,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:49:40.573Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","contentLength":44964,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:49:40.573Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":35748,"success":true,"contentLength":44964,"extractionMethod":"structured","wordCount":938},"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st"}}
{"timestamp":"2025-07-27T21:50:38.231Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-3","duration":93380,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue"}}
{"timestamp":"2025-07-27T21:50:38.232Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":3,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","reason":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:50:38.183Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","processingDuration":93380,"urlIndex":3,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:50:48.810Z","level":"INFO","component":"run-test-pipeline","message":"⏳ Rate limiting delay","context":{"pipeline":"run-test-pipeline","delayMs":3000,"reason":"OpenAI rate limiting"}}
{"timestamp":"2025-07-27T21:50:48.771Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":4,"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","reason":"Failed to create candidate article: Enhancement failed for article \"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:50:48.727Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","processingDuration":103924,"urlIndex":4,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:50:48.770Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-starten-41121301-19-26405537","processingId":"test-url-4","duration":103924,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue"}}
{"timestamp":"2025-07-27T21:50:51.812Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":3,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foer...","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-pa...","domain":"www.wallstreet-online.de"}]}}
{"timestamp":"2025-07-27T21:50:51.813Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-denkt-ki-partnerschaft-openai-anthropic","processingId":"test-url-6","pipeline":"run-test-pipeline","urlIndex":6,"extractedTitle":"apple plant siri neustart paukenschlag apple denkt ki partnerschaft openai anthropic","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T21:50:51.812Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":6,"totalUrls":9,"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T21:50:51.812Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":5,"totalUrls":9,"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T21:50:51.812Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","processingId":"test-url-5","pipeline":"run-test-pipeline","urlIndex":5,"extractedTitle":"oelpreise fallen warten foerderbeschluss opec","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T21:50:51.839Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:50:51.839Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:50:51.845Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:50:51.845Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:51:09.432Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":17593,"success":true,"contentLength":20550,"extractionMethod":"structured","wordCount":327},"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec"}}
{"timestamp":"2025-07-27T21:51:09.432Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","contentLength":20550,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:51:48.629Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","processingDuration":56817,"urlIndex":5,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Ölpreise fallen weiter - Warten auf Förderbeschluss der Opec+ - 01.07.2025\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Ölpreise fallen weiter - Warten auf Förderbeschluss der Opec+ - 01.07.2025\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:51:48.674Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":5,"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","reason":"Failed to create candidate article: Enhancement failed for article \"Ölpreise fallen weiter - Warten auf Förderbeschluss der Opec+ - 01.07.2025\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Ölpreise fallen weiter - Warten auf Förderbeschluss der Opec+ - 01.07.2025\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Ölpreise fallen weiter - Warten auf Förderbeschluss der Opec+ - 01.07.2025\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:51:48.674Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","processingId":"test-url-5","duration":56817,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Ölpreise fallen weiter - Warten auf Förderbeschluss der Opec+ - 01.07.2025\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue"}}
{"timestamp":"2025-07-27T21:53:43.393Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":171548,"success":true,"contentLength":61194,"extractionMethod":"stealth","wordCount":737},"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den"}}
{"timestamp":"2025-07-27T21:53:43.394Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den","contentLength":61194,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:55:09.286Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T21:55:09.285Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T21:55:09.285Z","pipeline":"run-test-pipeline","testUrlCount":9,"testUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"},{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"},{"url":"https://www.manager-magazin.de/politik/deutschland/deutschland-braucht-neue-schulden-regeln-und-mehr...","domain":"www.manager-magazin.de"},{"url":"https://www.spiegel.de/wirtschaft/unternehmen/deutsche-bank-meldet-rekordgewinne-im-dritten-quartal-...","domain":"www.spiegel.de"},{"url":"https://www.bbc.co.uk/news/articles/c79q8g7q283o","domain":"www.bbc.co.uk"}]}}
{"timestamp":"2025-07-27T21:55:09.296Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T21:55:09.296Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T21:55:09.304Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T21:55:09.304Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":9}}
{"timestamp":"2025-07-27T21:55:09.304Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-neta...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T21:55:09.305Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:55:09.304Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":9,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:55:09.304Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"Test Article","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:55:09.305Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":9,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:55:09.320Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:55:09.320Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:55:09.326Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:55:09.326Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:56:04.085Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":54759,"success":true,"contentLength":31951,"extractionMethod":"stealth","wordCount":320},"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah"}}
{"timestamp":"2025-07-27T21:56:04.085Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","contentLength":31951,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:56:38.003Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","duration":88658,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation"}}
{"timestamp":"2025-07-27T21:56:38.004Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":1,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","reason":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:56:37.963Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","processingDuration":88658,"urlIndex":1,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:57:03.315Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","contentLength":55309,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:57:03.315Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":113995,"success":true,"contentLength":55309,"extractionMethod":"structured","wordCount":341},"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d"}}
{"timestamp":"2025-07-27T21:57:49.317Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","duration":159971,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation"}}
{"timestamp":"2025-07-27T21:57:49.318Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","reason":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","stack":"Error: Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:57:49.352Z","level":"INFO","component":"run-test-pipeline","message":"⏳ Rate limiting delay","context":{"pipeline":"run-test-pipeline","delayMs":3000,"reason":"OpenAI rate limiting"}}
{"timestamp":"2025-07-27T21:57:49.277Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","processingDuration":159971,"urlIndex":2,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","stack":"Error: Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:57:52.354Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":4,"totalUrls":9,"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T21:57:52.353Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-3","pipeline":"run-test-pipeline","urlIndex":3,"extractedTitle":"zurueckhaltung tesla aktie unter druck schwache verkaufszahlen und aerger mit trump belasten","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T21:57:52.353Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":2,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-sc...","domain":"www.onvista.de"}]}}
{"timestamp":"2025-07-27T21:57:52.353Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":3,"totalUrls":9,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T21:57:52.354Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-starten-41121301-19-26405537","processingId":"test-url-4","pipeline":"run-test-pipeline","urlIndex":4,"extractedTitle":"dax vorboerse dax duerfte mit leichtem schwung in den juli starten","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T21:57:52.368Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:57:52.374Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:57:52.374Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:57:52.368Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:58:32.917Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","contentLength":44962,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:58:32.917Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":40543,"success":true,"contentLength":44962,"extractionMethod":"structured","wordCount":939},"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st"}}
{"timestamp":"2025-07-27T22:00:32.833Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T22:00:32.833Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T22:00:32.833Z","pipeline":"run-test-pipeline","testUrlCount":9,"testUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"},{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"},{"url":"https://www.manager-magazin.de/politik/deutschland/deutschland-braucht-neue-schulden-regeln-und-mehr...","domain":"www.manager-magazin.de"},{"url":"https://www.spiegel.de/wirtschaft/unternehmen/deutsche-bank-meldet-rekordgewinne-im-dritten-quartal-...","domain":"www.spiegel.de"},{"url":"https://www.bbc.co.uk/news/articles/c79q8g7q283o","domain":"www.bbc.co.uk"}]}}
{"timestamp":"2025-07-27T22:00:32.845Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T22:00:32.845Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T22:00:32.851Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T22:00:32.851Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":9}}
{"timestamp":"2025-07-27T22:00:32.851Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":9,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T22:00:32.851Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-neta...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:00:32.851Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"Test Article","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T22:00:32.851Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":9,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:00:32.852Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:00:32.870Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:00:32.870Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:00:32.877Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:00:32.877Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:07:01.184Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":388307,"success":true,"contentLength":6066,"extractionMethod":"standard","wordCount":375},"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d"}}
{"timestamp":"2025-07-27T22:07:01.184Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","contentLength":6066,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:07:10.123Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","processingDuration":397271,"urlIndex":2,"errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:07:10.166Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","reason":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:07:10.166Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","duration":397271,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'."}}
{"timestamp":"2025-07-27T22:07:17.640Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":404770,"success":true,"contentLength":48,"extractionMethod":"structured","wordCount":46},"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah"}}
{"timestamp":"2025-07-27T22:07:17.643Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","contentLength":48,"hasPublishedDate":false,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:07:26.117Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","duration":413223,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Test Article\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'."}}
{"timestamp":"2025-07-27T22:07:26.074Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","processingDuration":413223,"urlIndex":1,"errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Test Article\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Test Article\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:07:26.117Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":1,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","reason":"Failed to create candidate article: Enhancement failed for article \"Test Article\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Test Article\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Test Article\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:07:26.154Z","level":"INFO","component":"run-test-pipeline","message":"⏳ Rate limiting delay","context":{"pipeline":"run-test-pipeline","delayMs":3000,"reason":"OpenAI rate limiting"}}
{"timestamp":"2025-07-27T22:07:29.155Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":3,"totalUrls":9,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:07:29.156Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":4,"totalUrls":9,"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T22:07:29.155Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":2,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-sc...","domain":"www.onvista.de"}]}}
{"timestamp":"2025-07-27T22:07:29.156Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-starten-41121301-19-26405537","processingId":"test-url-4","pipeline":"run-test-pipeline","urlIndex":4,"extractedTitle":"dax vorboerse dax duerfte mit leichtem schwung in den juli starten","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T22:07:29.156Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-3","pipeline":"run-test-pipeline","urlIndex":3,"extractedTitle":"zurueckhaltung tesla aktie unter druck schwache verkaufszahlen und aerger mit trump belasten","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:07:29.167Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:07:29.167Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:07:29.170Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:07:29.170Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:07:58.834Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","contentLength":73296,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:07:58.834Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":29667,"success":true,"contentLength":73296,"extractionMethod":"structured","wordCount":1408},"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza"}}
{"timestamp":"2025-07-27T22:08:06.194Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","processingDuration":37037,"urlIndex":3,"errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:08:06.236Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-3","duration":37037,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'."}}
{"timestamp":"2025-07-27T22:08:06.236Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":3,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","reason":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:12:48.587Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T22:12:48.587Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T22:12:48.586Z","pipeline":"run-test-pipeline","testUrlCount":9,"testUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"},{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"},{"url":"https://www.manager-magazin.de/politik/deutschland/deutschland-braucht-neue-schulden-regeln-und-mehr...","domain":"www.manager-magazin.de"},{"url":"https://www.spiegel.de/wirtschaft/unternehmen/deutsche-bank-meldet-rekordgewinne-im-dritten-quartal-...","domain":"www.spiegel.de"},{"url":"https://www.bbc.co.uk/news/articles/c79q8g7q283o","domain":"www.bbc.co.uk"}]}}
{"timestamp":"2025-07-27T22:12:48.601Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T22:12:48.601Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T22:12:48.612Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"Test Article","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T22:12:48.611Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T22:12:48.611Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":9}}
{"timestamp":"2025-07-27T22:12:48.611Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-neta...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:12:48.611Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":9,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T22:12:48.612Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:12:48.612Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":9,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:12:48.632Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:12:48.632Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:12:48.639Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:12:48.639Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:18:52.696Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":364056,"success":true,"contentLength":10662,"extractionMethod":"standard","wordCount":409},"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d"}}
{"timestamp":"2025-07-27T22:18:52.696Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","contentLength":10662,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:19:01.650Z","level":"INFO","component":"run-test-pipeline","message":"📅 Publication date extracted","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","publishedDate":"2024-02-08T00:00:00.000Z","extractionSource":"firecrawl-metadata"}}
{"timestamp":"2025-07-27T22:19:01.650Z","level":"INFO","component":"run-test-pipeline","message":"📊 📝 Candidate article created","context":{"metrics":{"articleCreationDurationMs":8953,"articleId":"150","finalTitle":"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Sie","contentLength":10662},"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d"}}
{"timestamp":"2025-07-27T22:19:01.666Z","level":"INFO","component":"run-test-pipeline","message":"✅ Processing completed","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","duration":373054,"success":true,"pipeline":"run-test-pipeline","articleId":"150","keywordStatus":"Keywords matched","firecrawlDuration":364056,"articleCreationDuration":8953}}
{"timestamp":"2025-07-27T22:19:01.666Z","level":"INFO","component":"run-test-pipeline","message":"✅ URL accepted","context":{"pipeline":"run-test-pipeline","urlIndex":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","title":"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Sie","articleId":"150"}}
{"timestamp":"2025-07-27T22:20:33.902Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":465270,"success":false,"contentLength":0,"extractionMethod":"standard","wordCount":0},"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah"}}
{"timestamp":"2025-07-27T22:20:33.902Z","level":"ERROR","component":"run-test-pipeline","message":"Content extraction failed","context":{"pipeline":"run-test-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","isRateLimited":false,"firecrawlDuration":465270,"errorDetails":"All fallback strategies failed","errorCategory":"unknown_error"},"error":{"name":"Error","message":"All fallback strategies failed","stack":"Error: All fallback strategies failed\n    at processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:627:55)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:20:34.044Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","duration":465432,"success":false,"pipeline":"run-test-pipeline","reason":"All fallback strategies failed","isRateLimited":false}}
{"timestamp":"2025-07-27T22:20:34.044Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":1,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","reason":"All fallback strategies failed","errorCategory":"unknown_error"},"error":{"name":"Error","message":"All fallback strategies failed","stack":"Error: All fallback strategies failed\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:20:34.085Z","level":"INFO","component":"run-test-pipeline","message":"⏳ Rate limiting delay","context":{"pipeline":"run-test-pipeline","delayMs":3000,"reason":"OpenAI rate limiting"}}
{"timestamp":"2025-07-27T22:20:37.086Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":3,"totalUrls":9,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:20:37.087Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-starten-41121301-19-26405537","processingId":"test-url-4","pipeline":"run-test-pipeline","urlIndex":4,"extractedTitle":"dax vorboerse dax duerfte mit leichtem schwung in den juli starten","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T22:20:37.086Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-3","pipeline":"run-test-pipeline","urlIndex":3,"extractedTitle":"zurueckhaltung tesla aktie unter druck schwache verkaufszahlen und aerger mit trump belasten","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:20:37.086Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":2,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-sc...","domain":"www.onvista.de"}]}}
{"timestamp":"2025-07-27T22:20:37.087Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":4,"totalUrls":9,"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"}}
{"timestamp":"2025-07-27T22:20:37.105Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:20:37.105Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:20:37.118Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:20:37.118Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:20:50.367Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","contentLength":73458,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:20:50.367Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":13249,"success":true,"contentLength":73458,"extractionMethod":"structured","wordCount":1420},"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza"}}
{"timestamp":"2025-07-27T22:21:04.807Z","level":"INFO","component":"run-test-pipeline","message":"📅 Publication date extracted","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","publishedDate":"2025-07-27T15:55:00.000Z","extractionSource":"firecrawl-metadata"}}
{"timestamp":"2025-07-27T22:21:04.807Z","level":"INFO","component":"run-test-pipeline","message":"📊 📝 Candidate article created","context":{"metrics":{"articleCreationDurationMs":14440,"articleId":"151","finalTitle":"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.ne","contentLength":73458},"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza"}}
{"timestamp":"2025-07-27T22:21:04.821Z","level":"INFO","component":"run-test-pipeline","message":"✅ URL accepted","context":{"pipeline":"run-test-pipeline","urlIndex":3,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","title":"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.ne","articleId":"151"}}
{"timestamp":"2025-07-27T22:21:04.821Z","level":"INFO","component":"run-test-pipeline","message":"✅ Processing completed","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-3","duration":27735,"success":true,"pipeline":"run-test-pipeline","articleId":"151","keywordStatus":"Keywords matched","firecrawlDuration":13249,"articleCreationDuration":14440}}
{"timestamp":"2025-07-27T22:24:07.430Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":210325,"success":true,"contentLength":44684,"extractionMethod":"stealth","wordCount":1148},"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st"}}
{"timestamp":"2025-07-27T22:24:07.430Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","contentLength":44684,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:24:25.102Z","level":"INFO","component":"run-test-pipeline","message":"📅 Publication date extracted","context":{"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","publishedDate":"2025-06-29T23:00:00.000Z","extractionSource":"firecrawl-metadata"}}
{"timestamp":"2025-07-27T22:24:25.102Z","level":"INFO","component":"run-test-pipeline","message":"📊 📝 Candidate article created","context":{"metrics":{"articleCreationDurationMs":17672,"articleId":"152","finalTitle":"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista","contentLength":44684},"pipeline":"run-test-pipeline","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st"}}
{"timestamp":"2025-07-27T22:24:25.118Z","level":"INFO","component":"run-test-pipeline","message":"✅ Processing completed","context":{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-starten-41121301-19-26405537","processingId":"test-url-4","duration":228031,"success":true,"pipeline":"run-test-pipeline","articleId":"152","keywordStatus":"Keywords matched","firecrawlDuration":210325,"articleCreationDuration":17672}}
{"timestamp":"2025-07-27T22:24:25.118Z","level":"INFO","component":"run-test-pipeline","message":"✅ URL accepted","context":{"pipeline":"run-test-pipeline","urlIndex":4,"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st","title":"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista","articleId":"152"}}
{"timestamp":"2025-07-27T22:24:25.118Z","level":"INFO","component":"run-test-pipeline","message":"⏳ Rate limiting delay","context":{"pipeline":"run-test-pipeline","delayMs":3000,"reason":"OpenAI rate limiting"}}
{"timestamp":"2025-07-27T22:24:28.119Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":5,"totalUrls":9,"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T22:24:28.119Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":6,"totalUrls":9,"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T22:24:28.119Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-denkt-ki-partnerschaft-openai-anthropic","processingId":"test-url-6","pipeline":"run-test-pipeline","urlIndex":6,"extractedTitle":"apple plant siri neustart paukenschlag apple denkt ki partnerschaft openai anthropic","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T22:24:28.119Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","processingId":"test-url-5","pipeline":"run-test-pipeline","urlIndex":5,"extractedTitle":"oelpreise fallen warten foerderbeschluss opec","domain":"www.wallstreet-online.de"}}
{"timestamp":"2025-07-27T22:24:28.119Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":3,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foer...","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-pa...","domain":"www.wallstreet-online.de"}]}}
{"timestamp":"2025-07-27T22:24:28.135Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:24:28.135Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:24:28.201Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:24:28.201Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:26:27.557Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T22:26:27.557Z","pipeline":"run-test-pipeline","testUrlCount":2,"testUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:26:27.557Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T22:26:27.568Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T22:26:27.568Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T22:26:27.573Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":2}}
{"timestamp":"2025-07-27T22:26:27.573Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":1,"batchSize":2,"batchUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...","domain":"www.finanzen.net"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:26:27.574Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"zurueckhaltung tesla aktie unter druck schwache verkaufszahlen und aerger mit trump belasten","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:26:27.573Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T22:26:27.574Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:26:27.574Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:26:27.574Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":2,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:26:27.583Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:26:27.583Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:26:27.590Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:26:27.590Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:26:46.709Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","contentLength":73250,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:26:46.709Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":19126,"success":true,"contentLength":73250,"extractionMethod":"structured","wordCount":1400},"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza"}}
{"timestamp":"2025-07-27T22:27:27.604Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","processingDuration":60030,"urlIndex":1,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:669:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:202:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:269:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:27:27.649Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":1,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","reason":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)","stack":"Error: Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:227:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:269:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:27:27.649Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-1","duration":60030,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)"}}
{"timestamp":"2025-07-27T22:31:31.534Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":303944,"success":true,"contentLength":55309,"extractionMethod":"structured","wordCount":341},"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d"}}
{"timestamp":"2025-07-27T22:31:31.535Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","contentLength":55309,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:32:19.240Z","level":"ERROR","component":"run-test-pipeline","message":"❌ Processing failed","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","duration":351626,"success":false,"pipeline":"run-test-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)"}}
{"timestamp":"2025-07-27T22:32:19.200Z","level":"ERROR","component":"run-test-pipeline","message":"Failed to process test URL","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","processingDuration":351626,"urlIndex":2,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)","stack":"Error: Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:669:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:202:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:269:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:32:19.240Z","level":"ERROR","component":"run-test-pipeline","message":"💥 URL processing error","context":{"pipeline":"run-test-pipeline","urlIndex":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","reason":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)","stack":"Error: Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)\n    at eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:227:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:269:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:32:19.303Z","level":"WARN","component":"run-test-pipeline","message":"⚠️ Processing errors encountered","context":{"pipeline":"run-test-pipeline","errorCount":2,"errors":["https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049: Failed to create candidate article: Enhancement failed for article \"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)","https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed: Failed to create candidate article: Enhancement failed for article \"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)"]}}
{"timestamp":"2025-07-27T22:32:19.303Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test content pipeline completed successfully","context":{"pipeline":"run-test-pipeline","timing":{"processingTimeMs":351721,"processingTimeSeconds":352},"results":{"urlsProcessed":2,"articlesAccepted":0,"articlesRejected":0,"errorCount":2,"successRate":0},"database":{"totalArticles":0,"candidateArticles":0,"publishedArticles":0},"optimizations":{"parallelProcessing":"2 concurrent URLs","rateLimiting":"3s delays between batches","costReduction":"70% (single API call vs 7 calls)","performanceBoost":"70% faster via parallel processing"},"testConfiguration":{"totalTestUrls":2,"germanUrls":8,"englishUrls":1,"concurrency":2}}}
{"timestamp":"2025-07-27T22:39:49.466Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T22:39:49.466Z","pipeline":"run-test-pipeline","testUrlCount":2,"testUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:39:49.466Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T22:39:49.471Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T22:39:49.471Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T22:39:49.475Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T22:39:49.476Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":2}}
{"timestamp":"2025-07-27T22:39:49.476Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":1,"batchSize":2,"batchUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...","domain":"www.finanzen.net"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:39:49.476Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"zurueckhaltung tesla aktie unter druck schwache verkaufszahlen und aerger mit trump belasten","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:39:49.476Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:39:49.476Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:39:49.476Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":2,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:39:49.504Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:39:49.504Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:39:49.511Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:39:49.511Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:40:46.462Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T22:40:46.462Z","level":"INFO","component":"run-test-pipeline","message":"🧪 Test pipeline started","context":{"startTime":"2025-07-27T22:40:46.462Z","pipeline":"run-test-pipeline","testUrlCount":2,"testUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:40:46.477Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T22:40:46.477Z","level":"INFO","component":"run-test-pipeline","message":"✅ Database validation passed","context":{"pipeline":"run-test-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T22:40:46.485Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":1,"totalUrls":2,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:40:46.485Z","level":"INFO","component":"run-test-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"run-test-pipeline","batchNumber":1,"totalBatches":1,"batchSize":2,"batchUrls":[{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...","domain":"www.finanzen.net"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T22:40:46.485Z","level":"INFO","component":"run-test-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"run-test-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":2}}
{"timestamp":"2025-07-27T22:40:46.485Z","level":"INFO","component":"run-test-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"run-test-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T22:40:46.486Z","level":"INFO","component":"run-test-pipeline","message":"📰 Processing URL","context":{"pipeline":"run-test-pipeline","urlIndex":2,"totalUrls":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:40:46.486Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-1","pipeline":"run-test-pipeline","urlIndex":1,"extractedTitle":"zurueckhaltung tesla aktie unter druck schwache verkaufszahlen und aerger mit trump belasten","domain":"www.finanzen.net"}}
{"timestamp":"2025-07-27T22:40:46.486Z","level":"INFO","component":"run-test-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"run-test-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T22:40:46.532Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:40:46.532Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:40:46.539Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T22:40:46.539Z","level":"INFO","component":"run-test-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"run-test-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T22:43:39.248Z","level":"INFO","component":"run-test-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","contentLength":73291,"hasPublishedDate":true,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T22:43:39.248Z","level":"INFO","component":"run-test-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":172715,"success":true,"contentLength":73291,"extractionMethod":"standard","wordCount":1942},"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza"}}
{"timestamp":"2025-07-27T22:43:56.977Z","level":"INFO","component":"run-test-pipeline","message":"📊 📝 Candidate article created","context":{"metrics":{"articleCreationDurationMs":17729,"articleId":"153","finalTitle":"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.ne","contentLength":73291},"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza"}}
{"timestamp":"2025-07-27T22:43:56.977Z","level":"INFO","component":"run-test-pipeline","message":"📅 Publication date extracted","context":{"pipeline":"run-test-pipeline","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","publishedDate":"2025-07-27T14:32:00.000Z","extractionSource":"firecrawl-metadata"}}
{"timestamp":"2025-07-27T22:43:57.002Z","level":"INFO","component":"run-test-pipeline","message":"✅ Processing completed","context":{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","processingId":"test-url-1","duration":190516,"success":true,"pipeline":"run-test-pipeline","articleId":"153","keywordStatus":"Keywords matched","firecrawlDuration":172715,"articleCreationDuration":17729}}
{"timestamp":"2025-07-27T22:43:57.002Z","level":"INFO","component":"run-test-pipeline","message":"✅ URL accepted","context":{"pipeline":"run-test-pipeline","urlIndex":1,"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza","title":"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.ne","articleId":"153"}}
